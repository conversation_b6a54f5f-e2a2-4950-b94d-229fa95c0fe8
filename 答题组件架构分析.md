# 答题组件架构分析

## 概述

本文档基于 `apps/stu/app/exercise/page.tsx` 和 `apps/stu/app/views/course/course-widgets-loader-view.tsx` 两个核心文件，梳理了学生端答题组件的完整架构，包括分层设计、依赖关系、数据流和组件类图。

## 1. 整体架构设计

### 1.1 MVVM 架构模式

答题系统采用 MVVM（Model-View-ViewModel）架构模式，实现了清晰的分层和职责分离：

```mermaid
graph TD
    A[View 视图层] --> B[ViewModel 视图模型层]
    B --> C[Model 数据模型层]
    C --> D[Backend API]
    
    A1[ExerciseView] --> B1[useQuestionSubmissionVM]
    A2[ChoiceQuestionView] --> B2[useChoiceQuestionViewModel]
    A3[CourseWidgetsLoaderView] --> B3[useCourseViewContext]
    
    B1 --> C1[useGetNextQuestion]
    B2 --> C2[useSubmitStudyAnswer]
    B3 --> C3[useCourseWidgetModel]
```

### 1.2 Context-First 架构

采用 Context-First 零参数架构，通过 Context 传递所有必要数据，避免 props drilling：

- **ExerciseContext**: 答题核心上下文
- **CourseViewContext**: 课程视图上下文  
- **QuestionViewContext**: 题目视图上下文
- **ClientProvider**: 客户端环境上下文

## 2. 分层架构详解

### 2.1 视图层 (View Layer)

#### 2.1.1 页面入口组件

```typescript
// 答题页面入口
ExercisePreviewPage
├── ExercisePageContent
    ├── BaseExerciseEntry (巩固/拓展练习)
    └── WrongBookExerciseEntry (错题本练习)

// 课程中的答题组件
CourseWidgetsLoaderView
├── WidgetLoader
    └── ExerciseInCourseView
```

#### 2.1.2 核心视图组件

```typescript
// 主答题视图
ExerciseView (packages/core/new-exercise/view/main-view.tsx)
├── ExerciseViewContent
    ├── BackButton
    ├── ProgressBar  
    ├── TimerDisplay
    ├── QuestionViewContent
    ├── TransitionView
    └── FeedbackView

// 题目视图
QuestionView
├── ChoiceQuestionView (选择题)
├── FillBlankQuestionView (填空题)
└── QuestionActionButtons
```

### 2.2 视图模型层 (ViewModel Layer)

#### 2.2.1 核心 ViewModels

```typescript
// 答案提交逻辑
useQuestionSubmissionVM
├── 策略模式验证
├── 统一提交流程
├── 进度条管理
└── 埋点追踪

// 选择题专用逻辑
useChoiceQuestionViewModel
├── 选项选择处理
├── 多选题逻辑
└── 母子题导航

// 课程序列管理
useCourseSequenceViewmodel
├── 组件切换逻辑
├── 进度管理
└── 状态同步
```

#### 2.2.2 专用 ViewModels

```typescript
useTimerVM          // 计时器管理
useExitViewModel    // 退出逻辑
useQuestionTrackVM  // 埋点追踪
useFillBlankViewModel // 填空题逻辑
```

### 2.3 状态管理层 (Store Layer)

#### 2.3.1 核心 Store

```typescript
// 题目状态管理 (基于 Preact Signals)
QuestionStore
├── questionStatus: Signal<QuestionStatus>
├── userAnswerDataMap: Signal<Map<string, Answer[]>>
├── currentQuestion: Signal<StudyQuestionInfo>
├── progressBarState: Signal<ProgressBarState>
├── timerState: Signal<TimerState>
└── transitionState: Signal<TransitionState>

// 预览模式状态
PreviewStore
├── previewConfig: Signal<PreviewConfig>
├── previewCurrentIndex: Signal<number>
└── previewQuestionList: Signal<Question[]>
```

### 2.4 数据模型层 (Model Layer)

#### 2.4.1 API 模型

```typescript
// 核心答题 API
useGetNextQuestion    // 获取下一题
useSubmitStudyAnswer  // 提交答案
useExitStudySession   // 退出学习

// 错题本 API  
useAddWrongQuestion     // 添加错题
useRemoveWrongQuestion  // 移除错题
useGetErrorReasonTags   // 获取错误原因标签

// 课程组件 API
useCourseWidgetModel  // 获取课程组件数据
useQuestionList       // 获取题目列表
```

## 3. 数据流架构

### 3.1 答题数据流

```mermaid
sequenceDiagram
    participant U as User
    participant V as View
    participant VM as ViewModel  
    participant S as Store
    participant M as Model
    participant API as Backend

    U->>V: 用户操作
    V->>VM: 触发事件
    VM->>S: 更新状态
    S->>V: 响应式更新
    VM->>M: 调用API
    M->>API: HTTP请求
    API->>M: 返回数据
    M->>S: 更新状态
    S->>V: 自动重渲染
```

### 3.2 课程组件数据流

```mermaid
sequenceDiagram
    participant CW as CourseWidgetsLoader
    participant WL as WidgetLoader
    participant CM as CourseWidgetModel
    participant EV as ExerciseView
    participant QS as QuestionStore

    CW->>WL: 渲染组件列表
    WL->>CM: 请求组件数据
    CM->>WL: 返回组件配置
    WL->>EV: 渲染答题组件
    EV->>QS: 初始化状态
    QS->>EV: 提供响应式状态
```

## 4. 组件类图

### 4.1 核心组件类图

```mermaid
classDiagram
    class ExerciseView {
        +studyType: StudyType
        +studySessionId: number
        +widgetIndex?: number
        +onComplete(): void
        +onBack(): void
        +render(): JSX.Element
    }
    
    class QuestionStore {
        +questionStatus: Signal~QuestionStatus~
        +currentQuestion: Signal~StudyQuestionInfo~
        +userAnswerDataMap: Signal~Map~
        +updateUserAnswer(): void
        +submitAnswer(): Promise~void~
        +getNextQuestion(): Promise~void~
    }
    
    class ExerciseContext {
        +questionStore: QuestionStore
        +studyType: StudyType
        +isPreview: boolean
        +clientContext: ClientContext
    }
    
    class CourseViewContext {
        +currentIndex: Signal~number~
        +widgetList: CourseWidget[]
        +next(): void
        +goto(index: number): void
        +exit(): void
    }
    
    ExerciseView --> QuestionStore
    ExerciseView --> ExerciseContext
    CourseWidgetsLoaderView --> CourseViewContext
    QuestionStore --> ExerciseModels
```

### 4.2 ViewModel 类图

```mermaid
classDiagram
    class QuestionSubmissionVM {
        +submitAnswer(): Promise~void~
        +submitSelfEvaluation(): Promise~void~
        +giveUpAnswer(): Promise~void~
        +validateAnswer(): boolean
    }
    
    class ChoiceQuestionViewModel {
        +handleOptionSelect(): void
        +isOptionSelected(): boolean
        +getSelectedOptions(): string[]
    }
    
    class TimerVM {
        +isTimerActive: boolean
        +handleTimeUpdate(): void
        +startTimer(): void
        +stopTimer(): void
    }
    
    QuestionSubmissionVM --> QuestionStore
    ChoiceQuestionViewModel --> QuestionStore
    TimerVM --> QuestionStore
```

## 5. 依赖关系图

### 5.1 模块依赖

```mermaid
graph TD
    A[apps/stu/exercise] --> B[packages/core/new-exercise]
    A --> C[apps/stu/models]
    A --> D[apps/stu/providers]
    
    B --> E[packages/core/types]
    B --> F[packages/core/enums]
    B --> G[packages/ui]
    
    C --> H[packages/core/new-exercise/models]
    D --> I[packages/core/context]
```

### 5.2 组件依赖层次

```mermaid
graph TD
    A[ExercisePreviewPage] --> B[BaseExerciseEntry]
    A --> C[WrongBookExerciseEntry]
    
    B --> D[ExerciseView]
    C --> D
    
    D --> E[QuestionViewContent]
    D --> F[ProgressBar]
    D --> G[TimerDisplay]
    
    E --> H[ChoiceQuestionView]
    E --> I[FillBlankQuestionView]
    
    J[CourseWidgetsLoaderView] --> K[WidgetLoader]
    K --> L[ExerciseInCourseView]
    L --> D
```

## 6. 设计模式应用

### 6.1 策略模式
- **用途**: 不同题型的答案验证逻辑
- **实现**: `ValidationStrategy` 接口，支持选择题、填空题等不同验证策略

### 6.2 观察者模式  
- **用途**: 响应式状态管理
- **实现**: 基于 Preact Signals 的细粒度状态订阅

### 6.3 工厂模式
- **用途**: 根据学习类型创建不同的答题组件
- **实现**: `ExercisePageContent` 中的条件渲染逻辑

### 6.4 上下文模式
- **用途**: 跨组件状态共享
- **实现**: Context-First 架构，避免 props drilling

## 7. 关键特性

### 7.1 响应式状态管理
- 基于 Preact Signals 实现细粒度响应式更新
- 避免不必要的组件重渲染
- 支持复杂状态的高效管理

### 7.2 多模式支持
- **AI课程模式**: 课程中的练习组件
- **巩固练习**: 独立的练习模式  
- **拓展练习**: 扩展学习内容
- **错题本**: 错题复习模式

### 7.3 预览与正式模式
- **预览模式**: 查看已完成的题目和答案
- **正式模式**: 实时答题和提交

### 7.4 性能优化
- 组件懒加载和条件渲染
- SWR 缓存和重复请求去重
- 虚拟滚动和内存管理

## 8. 核心类型定义

### 8.1 答题相关类型

```typescript
// 题目状态枚举 - 表示当前题目的作答状态
enum QuestionStatus {
  LOADING = "loading",        // 题目加载中
  ANSWERING = "answering",    // 正在作答中
  SUBMITTED = "submitted",    // 已提交答案
  UNCERTAIN = "uncertain"     // 不确定状态（用户选择"不会"）
}

// 学习类型枚举 - 区分不同的学习场景
enum StudyType {
  AI_COURSE = 1,              // AI课程中的练习
  REINFORCEMENT_EXERCISE = 2, // 巩固练习
  EXPAND_EXERCISE = 3,        // 拓展练习
  WRONG_QUESTION_BANK = 8     // 错题本练习
}

// 答案验证类型 - 表示答案的正确性
enum AnswerVerifyType {
  NOT_ANSWERED = 0,      // 未作答
  CORRECT = 1,           // 答案正确
  WRONG = 2,             // 答案错误
  PARTIAL_CORRECT = 3    // 部分正确（多选题或填空题）
}

// 输入模式类型 - 表示用户的答题方式
enum InputModeType {
  KEYBOARD = 1,    // 键盘输入（文字答案）
  WHITEBOARD = 2,  // 白板手写（手写答案）
  CAMERA = 3       // 拍照上传（图片答案）
}
```

### 8.2 核心接口定义

```typescript
// 答题初始化参数 - 创建答题组件时需要的配置参数
interface ExerciseInitParams {
  studySessionId: number;                           // 学习会话ID，用于标识本次学习
  studyType: StudyType;                            // 学习类型，决定答题模式和UI样式
  widgetIndex?: number;                            // 组件索引，在课程中的位置
  initialQuestionData?: ApiGetNextQuestionData;    // 初始题目数据，避免首次加载
  onComplete: (totalTimeSpent?: number) => void;   // 完成回调，传递总耗时
  onBack: () => void;                              // 返回回调，处理退出逻辑
  clientContext: ClientContext;                    // 客户端上下文，包含设备相关方法
  displayConfig?: ExerciseDisplayConfig;          // 显示配置，控制UI元素显隐
  isPreview?: boolean;                             // 是否为预览模式
  previewConfig?: PreviewConfig;                   // 预览模式配置
}

// 学生答案格式 - 单个答案项的数据结构
interface Answer {
  index: number;                        // 答案在题目中的索引（填空题的第几个空）
  type: InputModeType;                  // 输入方式（键盘/白板/拍照）
  content: string;                      // 答案内容（文字形式）
  answerResult?: AnswerVerifyType;      // 判题结果（正确/错误/部分正确）
  selfEvaluation?: SelfEvaluateType;    // 学生自评结果
  whiteBoardData?: WhiteBoardData;      // 白板手写数据
  cameraData?: ImageFile[];             // 拍照上传的图片数据
}

// 题目信息 - 完整的题目数据结构
interface StudyQuestionInfo {
  questionId: string;              // 题目唯一标识
  questionType: QuestionType;      // 题目类型（选择题/填空题等）
  content: string;                 // 题目内容（HTML格式）
  options?: QuestionOption[];      // 选择题选项列表
  blanks?: BlankInfo[];           // 填空题空位信息
  correctAnswer?: string[];        // 标准答案（用于预览模式）
  explanation?: string;            // 题目解析
}
```

### 8.3 课程组件类型

```typescript
// 课程组件摘要 - 组件的基本信息，用于列表展示和导航
interface CourseWidgetSummaryWithoutStatus {
  index: number;                                      // 组件在课程中的序号
  name: string;                                       // 组件显示名称
  type: "exercise" | "guide" | "video" | "interactive"; // 组件类型
  cdnUrl?: string;                                    // CDN资源地址（用于guide类型）
}

// 课程组件数据 - 完整的组件数据，包含具体内容
type CourseWidget<T extends string> = {
  index: number;    // 组件序号
  name: string;     // 组件名称
  type: T;          // 组件类型（泛型约束）
  data: T extends "exercise" ? ExerciseWidgetData :      // 练习组件数据
        T extends "guide" ? GuideWidgetData :            // 引导组件数据
        T extends "video" ? VideoWidgetData :            // 视频组件数据
        T extends "interactive" ? InteractiveWidgetData : // 互动组件数据
        never;    // 类型安全保证
}
```

## 9. 状态管理详解

### 9.1 Signal Store 架构

```typescript
// 基于 Preact Signals 的状态管理 - 答题核心状态存储
class QuestionStore {
  // ========== 核心业务状态 ==========
  questionStatus = signal<QuestionStatus>("loading");           // 当前题目状态
  currentQuestion = signal<StudyQuestionInfo | null>(null);     // 当前题目信息
  userAnswerDataMap = signal<Map<string, Answer[]>>(new Map()); // 用户答案映射表

  // ========== UI 交互状态 ==========
  progressBarState = signal<ProgressBarState>({
    current: 0,        // 当前进度
    total: 0,          // 总题目数
    isVisible: true    // 是否显示进度条
  });

  timerState = signal<TimerState>({
    startTime: 0,      // 开始时间戳
    currentTime: 0,    // 当前时间戳
    isActive: false    // 计时器是否激活
  });

  transitionState = signal<TransitionState>({
    isVisible: false,  // 转场动画是否可见
    type: "next",      // 转场类型（下一题/上一题）
    duration: 300      // 动画持续时间（毫秒）
  });

  // ========== 计算属性 ==========
  // 判断当前题目是否已完成作答
  isAnswerComplete = computed(() => {
    const answers = this.getCurrentAnswers();
    return answers.every(answer => answer.content.trim() !== "");
  });

  // ========== 状态更新方法 ==========
  // 更新用户答案
  updateUserAnswer = (params: UpdateStudentAnswerParams) => {
    // 实现答案更新逻辑，包括验证和状态同步
  };

  // 提交答案到服务器
  submitAnswer = async () => {
    // 实现答案提交逻辑，包括网络请求和状态更新
  };
}
```

### 9.2 Context 状态共享

```typescript
// 答题上下文 - 提供答题相关的全局状态和配置
interface ExerciseContextType {
  questionStore: QuestionStore;                    // 题目状态管理器
  studyType: StudyType;                           // 学习类型，影响UI和逻辑
  studySessionId: number;                         // 学习会话ID
  widgetIndex?: number;                           // 组件索引（课程中使用）
  isPreview: boolean;                             // 是否为预览模式
  clientContext: ClientContext;                   // 客户端环境上下文
  exerciseDisplayConfig?: ExerciseDisplayConfig;  // 显示配置（控制UI元素）
}

// 课程视图上下文 - 管理课程中多个组件的状态和导航
interface CourseViewContextType {
  // ========== 序列管理 ==========
  currentIndex: Signal<number>;                           // 当前激活的组件索引
  total: number;                                          // 组件总数
  widgetList: CourseWidgetSummaryWithoutStatus[];        // 组件列表摘要

  // ========== 导航方法 ==========
  next: () => void;                    // 跳转到下一个组件
  goto: (index: number) => void;       // 跳转到指定索引的组件
  exit: () => void;                    // 退出课程

  // ========== 状态管理 ==========
  exerciseCompletedRecord: Signal<Record<number, boolean>>; // 练习完成记录
  isVersionChanged: Signal<boolean>;                        // 课程版本是否变更

  // ========== 配置信息 ==========
  knowledgeId: number;    // 知识点ID
  lessonId: number;       // 课程ID
  studySessionId: number; // 学习会话ID
  studyType: number;      // 学习类型
}
```

## 10. 性能优化策略

### 10.1 组件级优化

```typescript
// 1. 条件渲染避免不必要的组件创建
// 只加载当前、前一个和后一个组件，减少内存占用
const shouldLoad = useComputed(() => {
  return (
    index === currentIndex.value ||      // 当前组件
    index === currentIndex.value + 1 ||  // 下一个组件（预加载）
    index === currentIndex.value - 1     // 上一个组件（缓存）
  );
});

// 2. useMemo 缓存复杂计算结果
// 避免每次渲染都重新创建对象，减少子组件重渲染
const commonProps = useMemo(() => ({
  initialQuestionData: exerciseData.data,  // 初始题目数据
  widgetIndex,                            // 组件索引
  studySessionId,                         // 会话ID
  // ... 其他属性
}), [exerciseData.data, widgetIndex, studySessionId]); // 依赖项变化时才重新计算

// 3. useCallback 缓存事件处理器
// 避免每次渲染都创建新函数，防止子组件不必要的重渲染
const handleComplete = useCallback((totalTimeSpent?: number) => {
  if (totalTimeSpent) {
    reportCostTime(totalTimeSpent);  // 上报学习时长
  }
  next();  // 跳转到下一个组件
}, [next, reportCostTime]); // 依赖项稳定时函数引用不变
```

### 10.2 数据请求优化

```typescript
// SWR 配置优化 - 针对练习类型的特殊配置
const swrOptions = summary?.type === "exercise" ? {
  revalidateOnFocus: false,      // 禁用焦点重新验证（避免切换标签页时重复请求）
  revalidateOnReconnect: false,  // 禁用重连重新验证（避免网络恢复时重复请求）
  revalidateIfStale: false,      // 禁用过期重新验证（练习数据不需要实时更新）
  refreshWhenOffline: false,     // 禁用离线刷新
  refreshWhenHidden: false,      // 禁用隐藏时刷新
  dedupingInterval: 60000,       // 60秒内相同请求去重，避免重复调用
} : {};

// 条件请求避免无效调用 - 只在需要时才发起请求
const { data, isLoading, error } = useCourseWidgetModel({
  knowledgeId,                                    // 知识点ID
  lessonId,                                      // 课程ID
  summary: shouldLoad.value ? summary : undefined, // 只有需要加载时才传入summary
  nextQuestionParams,                            // 下一题参数
});
```

### 10.3 内存管理

```typescript
// 1. 及时清理事件监听器 - 防止内存泄漏
useEffect(() => {
  // 监听设备返回按钮事件
  const removeListener = listenDeviceBackAction?.((result) => {
    if (result.code === 0 && result.data?.event === "backPressed") {
      handleExitRequest(); // 处理返回按钮点击
    }
  });

  // 组件卸载时清理监听器
  return () => {
    removeListener?.(); // 移除事件监听器，防止内存泄漏
  };
}, [handleExitRequest, listenDeviceBackAction]);

// 2. 使用 AbortController 取消请求 - 避免组件卸载后的异步操作
useEffect(() => {
  const controller = new AbortController();

  // 发起可取消的网络请求
  fetchData({ signal: controller.signal });

  return () => {
    controller.abort(); // 组件卸载时取消未完成的请求
  };
}, []);
```

## 11. 错误处理机制

### 11.1 分层错误处理

```typescript
// Model 层错误处理 - 统一处理API调用错误
export const useGetNextQuestion = (params: GetNextQuestionParams) => {
  return useSWR(
    getApiKey(params),  // 生成缓存键
    async () => {
      try {
        const response = await api.getNextQuestion(params);
        return response.data;  // 返回题目数据
      } catch (error) {
        // 统一错误处理，包装为业务错误
        throw new ExerciseError("获取题目失败", error);
      }
    }
  );
};

// ViewModel 层错误处理 - 处理业务逻辑错误并提供用户反馈
export const useQuestionSubmissionVM = () => {
  const submitAnswer = async () => {
    try {
      setIsSubmitting(true);           // 设置提交状态
      await questionStore.submitAnswer(); // 执行提交逻辑
    } catch (error) {
      // 用户友好的错误提示
      showErrorToast("提交失败，请重试");
      console.error("Submit answer error:", error); // 开发调试信息
    } finally {
      setIsSubmitting(false);          // 重置提交状态
    }
  };
};

// View 层错误边界 - 捕获组件渲染错误
const ErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={
        <ErrorPage
          onRefresh={() => window.location.reload()} // 刷新页面恢复
        />
      }
    >
      {children}
    </ErrorBoundary>
  );
};
```

### 11.2 网络错误重试

```typescript
// 自动重试机制 - 配置网络请求的重试策略
const { data, error, mutate } = useSWR(
  apiKey,    // 请求的唯一标识
  fetcher,   // 数据获取函数
  {
    errorRetryCount: 3,        // 最大重试次数
    errorRetryInterval: 1000,  // 重试间隔（毫秒）
    onError: (error) => {
      // 记录错误日志，便于问题排查
      console.error("API Error:", error);

      // 特定错误码处理
      if (error.status === 401) {
        // 401未授权 - 重新登录
        redirectToLogin();
      } else if (error.status === 403) {
        // 403禁止访问 - 显示权限错误
        showErrorToast("没有访问权限");
      } else if (error.status >= 500) {
        // 5xx服务器错误 - 显示服务器错误
        showErrorToast("服务器错误，请稍后重试");
      }
    }
  }
);
```

## 12. 核心方法和属性详解

### 12.1 QuestionStore 核心方法

```typescript
class QuestionStore {
  // ========== 状态更新方法 ==========

  /**
   * 更新用户答案
   * @param params.questionId - 题目ID
   * @param params.blankIndex - 空位索引（填空题使用）
   * @param params.type - 输入类型（键盘/白板/拍照）
   * @param params.content - 答案内容
   * @param params.whiteBoardData - 白板数据（可选）
   * @param params.cameraData - 拍照数据（可选）
   */
  updateUserAnswer(params: UpdateStudentAnswerParams): void;

  /**
   * 提交答案到服务器
   * @returns Promise<void> - 提交结果
   * @throws ExerciseError - 提交失败时抛出错误
   */
  submitAnswer(): Promise<void>;

  /**
   * 获取下一题
   * @returns Promise<void> - 获取结果
   * @throws ExerciseError - 获取失败时抛出错误
   */
  getNextQuestion(): Promise<void>;

  /**
   * 初始化配置
   * @param config.studySessionId - 学习会话ID
   * @param config.studyType - 学习类型
   * @param config.widgetIndex - 组件索引
   * @param config.subjectId - 学科ID
   * @param config.isEnglish - 是否为英语学科
   */
  initializeConfig(config: ExerciseConfig): void;

  // ========== 状态查询方法 ==========

  /**
   * 获取当前题目的答案列表
   * @returns Answer[] - 答案数组
   */
  getCurrentAnswers(): Answer[];

  /**
   * 重置所有状态到初始值
   */
  resetState(): void;
}
```

### 12.2 ViewModel 核心方法

```typescript
// 答案提交 ViewModel
class QuestionSubmissionVM {
  /**
   * 提交答案（主要方法）
   * - 验证答案完整性
   * - 调用API提交
   * - 更新UI状态
   * - 处理错误情况
   */
  async submitAnswer(): Promise<void>;

  /**
   * 提交自评结果
   * @param evaluation - 自评类型（正确/错误/部分正确）
   */
  async submitSelfEvaluation(evaluation: SelfEvaluateType): Promise<void>;

  /**
   * 放弃作答
   * - 标记题目为放弃状态
   * - 跳转到下一题
   */
  async giveUpAnswer(): Promise<void>;

  /**
   * 验证答案格式和完整性
   * @returns boolean - 验证结果
   */
  validateAnswer(): boolean;
}

// 选择题 ViewModel
class ChoiceQuestionViewModel {
  /**
   * 处理选项点击
   * @param optionId - 选项ID
   * - 单选题：替换当前选择
   * - 多选题：切换选择状态
   */
  handleOptionSelect(optionId: string): void;

  /**
   * 判断选项是否已选择
   * @param optionId - 选项ID
   * @returns boolean - 是否已选择
   */
  isOptionSelected(optionId: string): boolean;

  /**
   * 获取所有已选择的选项
   * @returns string[] - 选项ID数组
   */
  getSelectedOptions(): string[];

  /**
   * 清空所有选择
   */
  clearSelection(): void;
}
```

### 12.3 Context 核心方法

```typescript
// 课程视图上下文
class CourseViewContext {
  /**
   * 跳转到下一个组件
   * - 更新当前索引
   * - 触发转场动画
   * - 记录学习进度
   */
  next(): void;

  /**
   * 跳转到指定索引的组件
   * @param index - 目标组件索引
   * - 验证索引有效性
   * - 更新当前索引
   * - 滚动到目标位置
   */
  goto(index: number): void;

  /**
   * 退出课程
   * - 保存学习进度
   * - 清理资源
   * - 返回上级页面
   */
  exit(): void;

  /**
   * 上报学习时长
   * @param timeSpent - 耗时（秒）
   * - 记录到后端
   * - 更新本地统计
   */
  reportCostTime(timeSpent: number): void;

  /**
   * 记录课程相关埋点事件
   * @param eventID - 事件ID
   * @param needWidgetInfo - 是否需要组件信息
   */
  trackEventWithLessonId(eventID: string, needWidgetInfo?: boolean): void;
}
```

### 12.4 Model 层 API 方法

```typescript
// 答题相关 API
class ExerciseModels {
  /**
   * 获取下一题
   * @param params.studySessionId - 学习会话ID
   * @param params.widgetIndex - 组件索引（可选）
   * @param params.questionId - 当前题目ID（可选）
   * @returns SWRResponse<StudyQuestionInfo> - 题目信息
   */
  useGetNextQuestion(params: GetNextQuestionParams): SWRResponse<StudyQuestionInfo>;

  /**
   * 提交答案
   * @param params.studySessionId - 学习会话ID
   * @param params.questionId - 题目ID
   * @param params.answers - 答案列表
   * @param params.timeSpent - 作答耗时
   * @returns SWRResponse<SubmitAnswerResponse> - 提交结果
   */
  useSubmitStudyAnswer(params: SubmitAnswerParams): SWRResponse<SubmitAnswerResponse>;

  /**
   * 退出学习会话
   * @param sessionId - 会话ID
   * @returns SWRResponse<void> - 退出结果
   */
  useExitStudySession(sessionId: number): SWRResponse<void>;

  /**
   * 生成API缓存键
   * @param params - 请求参数
   * @returns string - 缓存键
   */
  getApiKey(params: any): string;

  /**
   * 转换题目数据格式
   * @param data - 原始API数据
   * @returns StudyQuestionInfo - 标准化题目数据
   */
  transformQuestionData(data: any): StudyQuestionInfo;
}

// 课程组件 API
class CourseWidgetModel {
  /**
   * 获取课程组件数据
   * @param params.knowledgeId - 知识点ID
   * @param params.lessonId - 课程ID
   * @param params.summary - 组件摘要
   * @param params.nextQuestionParams - 下一题参数
   * @returns SWRResponse<CourseWidget> - 组件数据
   */
  useCourseWidgetModel(params: WidgetParams): SWRResponse<CourseWidget>;

  /**
   * 根据组件类型生成API URL
   * @param params - 组件参数
   * @returns string | null - API地址
   */
  getApiUrl(params: WidgetParams): string | null;

  /**
   * 转换组件数据格式
   * @param data - 原始API数据
   * @param summary - 组件摘要
   * @returns CourseWidget - 标准化组件数据
   */
  transformData(data: any, summary: CourseWidgetSummaryWithoutStatus): CourseWidget;
}
```

## 13. 总结

答题组件架构采用现代化的 MVVM 模式和 Context-First 设计，实现了：

1. **清晰的分层**: View-ViewModel-Model 职责分离
2. **响应式状态**: 基于 Signals 的高效状态管理
3. **模块化设计**: 高内聚低耦合的组件结构
4. **多模式支持**: 灵活适配不同学习场景
5. **性能优化**: 多种优化策略保证流畅体验
6. **错误处理**: 完善的错误处理和恢复机制
7. **类型安全**: 完整的 TypeScript 类型定义
8. **详细注释**: 每个方法和属性都有明确的用途说明

这种架构设计为答题系统提供了良好的可维护性、可扩展性和用户体验，同时确保了代码的健壮性和性能表现。通过详细的方法和属性注释，开发者可以快速理解每个组件的职责和使用方式。
